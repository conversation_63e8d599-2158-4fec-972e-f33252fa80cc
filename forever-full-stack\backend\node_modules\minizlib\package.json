{"name": "minizlib", "version": "2.1.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "MIT", "devDependencies": {"tap": "^14.6.9"}, "files": ["index.js", "constants.js"], "engines": {"node": ">= 8"}}