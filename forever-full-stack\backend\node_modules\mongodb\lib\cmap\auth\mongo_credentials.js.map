{"version": 3, "file": "mongo_credentials.js", "sourceRoot": "", "sources": ["../../../src/cmap/auth/mongo_credentials.ts"], "names": [], "mappings": ";;;AAGA,uCAIqB;AAErB,qCAAuD;AAEvD,2CAA0E;AAE1E;;GAEG;AACH,SAAS,uBAAuB,CAAC,KAAsB;IACrD,IAAI,KAAK,EAAE,CAAC;QACV,0DAA0D;QAC1D,uCAAuC;QACvC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,yBAAa,CAAC,oBAAoB,CAAC;gBAC1E,CAAC,CAAC,yBAAa,CAAC,oBAAoB;gBACpC,CAAC,CAAC,yBAAa,CAAC,kBAAkB,CAAC;QACvC,CAAC;IACH,CAAC;IAED,6CAA6C;IAC7C,OAAO,yBAAa,CAAC,oBAAoB,CAAC;AAC5C,CAAC;AAED,MAAM,yBAAyB,GAA6C;IAC1E,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;CACN,CAAC;AACF,MAAM,mBAAmB,GAAG,oEAAoE,CAAC;AAEjG,gBAAgB;AACH,QAAA,qBAAqB,GAAG;IACnC,eAAe;IACf,kBAAkB;IAClB,mBAAmB;IACnB,kBAAkB;IAClB,WAAW;IACX,WAAW;IACX,KAAK;CACN,CAAC;AAEF,uEAAuE;AACvE,MAAM,4BAA4B,GAChC,+FAA+F,CAAC;AA0DlG;;;GAGG;AACH,MAAa,gBAAgB;IAY3B,YAAY,OAAgC;QAC1C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,EAAE,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,yBAAa,CAAC,eAAe,CAAC;QACpE,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,EAAE,CAAC;QAE7D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;gBACpD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACxD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACpD,CAAC;YAED,IACE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,IAAI,IAAI;gBAClD,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,EACrC,CAAC;gBACD,IAAI,CAAC,mBAAmB,GAAG;oBACzB,GAAG,IAAI,CAAC,mBAAmB;oBAC3B,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;iBACjD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;YAC7F,IAAI,CAAC,mBAAmB,GAAG;gBACzB,GAAG,IAAI,CAAC,mBAAmB;gBAC3B,aAAa,EAAE,6BAAqB;aACrC,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,gEAAgE;IAChE,MAAM,CAAC,KAAuB;QAC5B,OAAO,CACL,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS;YAClC,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAChC,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAChC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAC7B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,oBAAoB,CAAC,KAAsB;QACzC,0EAA0E;QAC1E,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,uBAAuB,CAAC,KAAK,CAAC;gBACzC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;aAC9C,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ;QACN,IACE,CAAC,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,cAAc;YAC9C,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,aAAa;YAC9C,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,kBAAkB;YACnD,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,oBAAoB,CAAC;YACxD,CAAC,IAAI,CAAC,QAAQ,EACd,CAAC;YACD,MAAM,IAAI,oCAA4B,CAAC,oCAAoC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,YAAY,EAAE,CAAC;YAClD,IACE,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBACpC,IAAI,CAAC,mBAAmB,CAAC,WAAW,KAAK,OAAO,EAChD,CAAC;gBACD,MAAM,IAAI,iCAAyB,CACjC,6BAA6B,IAAI,CAAC,mBAAmB,CAAC,WAAW,6CAA6C,IAAI,CAAC,SAAS,IAAI,CACjI,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,MAAM,IAAI,iCAAyB,CACjC,0CAA0C,IAAI,CAAC,mBAAmB,CAAC,WAAW,UAAU,IAAI,CAAC,SAAS,IAAI,CAC3G,CAAC;YACJ,CAAC;YAED,IACE,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,KAAK,OAAO;gBAC/C,IAAI,CAAC,mBAAmB,CAAC,WAAW,KAAK,KAAK,CAAC;gBACjD,CAAC,IAAI,CAAC,mBAAmB,CAAC,cAAc,EACxC,CAAC;gBACD,MAAM,IAAI,iCAAyB,CAAC,4BAA4B,CAAC,CAAC;YACpE,CAAC;YAED,IACE,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBACpC,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,EACzE,CAAC;gBACD,MAAM,IAAI,iCAAyB,CACjC,mCAAmC,yBAAyB,CAAC,IAAI,CAC/D,GAAG,CACJ,gCAAgC,IAAI,CAAC,SAAS,IAAI,CACpD,CAAC;YACJ,CAAC;YAED,IACE,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBACrC,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa;gBACvC,CAAC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAC7C,CAAC;gBACD,MAAM,IAAI,iCAAyB,CACjC,gGAAgG,IAAI,CAAC,SAAS,IAAI,CACnH,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;gBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;gBACrD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,iCAAyB,CAAC,mBAAmB,CAAC,CAAC;gBAC3D,CAAC;gBACD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC7B,MAAM,IAAI,iCAAyB,CAAC,mBAAmB,CAAC,CAAC;oBAC3D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,wCAA4B,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACvD,gEAAgE;gBAChE,MAAM,IAAI,qBAAa,CACrB,mBAAmB,IAAI,CAAC,MAAM,oBAAoB,IAAI,CAAC,SAAS,cAAc,CAC/E,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC1E,gEAAgE;YAChE,MAAM,IAAI,qBAAa,CAAC,qDAAqD,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,yBAAa,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC3E,IAAI,IAAI,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;gBACzC,OAAO;YACT,CAAC;YACD,gEAAgE;YAChE,MAAM,IAAI,qBAAa,CAAC,iDAAiD,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,IAAI,KAAK,CAAC;QAClF,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,oCAA2B,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,qBAAa,CAAC,yCAAyC,gBAAgB,EAAE,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CACV,KAAmC,EACnC,OAAyC;QAEzC,OAAO,IAAI,gBAAgB,CAAC;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,EAAE,QAAQ,IAAI,EAAE;YACnD,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,EAAE,QAAQ,IAAI,EAAE;YACnD,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,KAAK,EAAE,SAAS,IAAI,yBAAa,CAAC,eAAe;YACjF,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,KAAK,EAAE,mBAAmB,IAAI,EAAE;YACpF,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE,IAAI,KAAK,EAAE,MAAM,IAAI,OAAO;SACjE,CAAC,CAAC;IACL,CAAC;CACF;AAnMD,4CAmMC"}