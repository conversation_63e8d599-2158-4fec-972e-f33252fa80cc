{"name": "@types/whatwg-url", "version": "11.0.5", "description": "TypeScript definitions for whatwg-url", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/whatwg-url", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "aomarks", "url": "https://github.com/aomarks"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/whatwg-url"}, "scripts": {}, "dependencies": {"@types/webidl-conversions": "*"}, "typesPublisherContentHash": "c6cfac1bbd7b2ef315fdad11fc9bdb6a8f0ae2b1c3ff057cfca7bc9880eeaa9d", "typeScriptVersion": "4.7"}